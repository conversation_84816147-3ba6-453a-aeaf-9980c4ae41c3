public class ArrayFun {
    public static void main(String[] args) {
        // Array of spaceship names
        String[] spaceshipNames = {
            "Millennium Falcon",      // Star Wars
            "USS Enterprise NCC-1701",// Star Trek
            "Serenity",               // Firefly
            "Battlestar Galactica",   // Battlestar Galactica
            "Event Horizon",          // Event Horizon
            "Discovery One",          // 2001: A Space Odyssey
            "Heart of Gold"           // Hitchhiker's Guide to the Galaxy
        };

        // Corresponding array of volumes in cubic meters (fictional where unknown)
        double[] spaceshipVolumes = {
            250_000.0,  // Millennium Falcon
            3_000_000.0,// USS Enterprise
            85_000.0,   // Serenity
            15_000_000.0,// Battlestar Galactica
            900_000.0,  // Event Horizon
            200_000.0,  // Discovery One
            1_500_000.0 // Heart of Gold
        };

        // Display both arrays together
        for (int i = 0; i <= spaceshipNames.length; i++) {
            System.out.println(spaceshipNames[i] + " has volume " + spaceshipVolumes[i] + " cubic meters.");
        }
    }
}