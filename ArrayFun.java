import java.util.Scanner;

public class ArrayFun {
    public static void main(String [] args) {
        Scanner kb = new Scanner(System.in);
        
       
        String[] spaceshipNames = {
            "Millennium Falcon",      // Star Wars
            "USS Enterprise NCC-1701",// Star Trek
            "Serenity",               // Firefly
            "Battlestar Galactica",   // Battlestar Galactica
            "Event Horizon",          // Event Horizon
            "Discovery One",          // 2001: A Space Odyssey
            "Heart of Gold"           // Hitchhiker's Guide to the Galaxy
        };

        
        double[] spaceshipVolumes = new double[spaceshipNames.length];

        
        for (int i = 0; i < spaceshipVolumes.length; i++) {
            System.out.print("Enter the volume (in cubic meters) for " + spaceshipNames[i] + ": ");
            spaceshipVolumes[i] = kb.nextDouble();
        }

       
        System.out.println("\nSpaceship Volumes:");
        for (int i = 0; i < spaceshipVolumes.length; i++) {
            System.out.println(spaceshipNames[i] + " has volume " + spaceshipVolumes[i] + " cubic meters.");
        }
    }
}
