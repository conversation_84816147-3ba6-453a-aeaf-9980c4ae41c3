import java.util.Scanner;

public class LetterGrade {
    public static void main(String [] args) {
        Scanner sc = new Scanner(System.in);
        System.out.print("Enter your score: ");
        int score = sc.nextInt();
        
        System.out.println(calcLetterGrade(score));
    }

    // We return a letter grade for the given score as follows:
    // A = 90-100
    // B = 80-89
    // C = 70-79
    // D = 60-69
    // F = 59 and below
    public static String calcLetterGrade(int score) {
        if (score >= 90) {
            return "A";
        } else if (score >= 80) {
            return "B";
        } else if (score >= 70) {
            return "C";
        } else if (score >= 60) {
            return "D";
        } else {
            return "F";
        }
    }
}
