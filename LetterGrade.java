import java.util.Scanner;

public class LetterGrade {
    public static void main(String [] args) {
        Scanner sc = new Scanner(System.in);
        System.out.print("Enter your score: ");
        int score = sc.nextInt();

        /*
        We have a correctly working method to calculate a letter grade given a score below
        TODO: We are not properly using the method. Fix the problems with the method call 
        and the print statement so we will see the proper letter grade displayed to the screen
        */
        String answer = calcLetterGrade(score);
        System.out.println("Your letter grade is: " + answer);
    }

    // We return a letter grade for the given score as follows:
    // A = 90-100
    // B = 80-89
    // C = 70-79
    // D = 60-69
    // F = 59 and below
    public static String calcLetterGrade(int score) {
        if (score >= 90) {
            return "A";
        } else if (score >= 80) {
            return "B";
        } else if (score >= 70) {
            return "C";
        } else if (score >= 60) {
            return "D";
        } else {
            return "F";
        }
    }
}
