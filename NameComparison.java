import java.util.Scanner;

public class NameComparison {
    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);
        String name1 = sc.nextLine();
        String name2 = sc.nextLine();

        int result = name1.compareTo(name2);

        if (result < 0) {
            System.out.println(name1 + " comes before " + name2);
        } else if (result > 0) {
            System.out.println(name1 + " comes after " + name2);
        } else {
            System.out.println("The names match!");
        }

        sc.close();
    }
}
